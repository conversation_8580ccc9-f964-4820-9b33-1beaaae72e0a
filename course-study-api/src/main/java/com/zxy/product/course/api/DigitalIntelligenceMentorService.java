package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数智导师对话记录服务接口
 */
@RemoteService(timeout = 30000)
public interface DigitalIntelligenceMentorService {
    /**
     * 根据用户ID查询最新的对话记录（使用配置的默认数量限制）
     * @param memberId 用户ID
     * @return 对话记录列表，按创建时间降序排列
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<DigitalIntelligenceMentor> findLatestConversationsByMemberId(String memberId);

    /**
     * 插入对话记录
     * @param digitalIntelligenceMentor 对话记录
     * @return 插入的对话记录
     */
    @Transactional
    DigitalIntelligenceMentor insert(DigitalIntelligenceMentor digitalIntelligenceMentor);

    /**
     * 更新对话记录的大模型响应
     * @param id 记录ID
     * @param botResponse 大模型响应内容
     */
    @Transactional
    void updateBotResponse(String id, String botResponse);

    /**
     * 提交问题给大模型并获取回答
     * @param memberId 用户ID
     * @param userQuery 用户问题
     * @return 创建的对话记录
     */
    @Transactional
    DigitalIntelligenceMentor askModel(String memberId, String userQuery);
}
